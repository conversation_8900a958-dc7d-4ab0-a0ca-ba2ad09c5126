# AI代码修改数据收集工具 - MCP服务器

这是一个基于FastMCP 2.0的MCP服务器，用于收集和分析开发者对AI生成代码的修改行为。

## 功能特性

- 📝 **代码修改收集**: 记录AI生成代码与人工修改后代码的对比
- 🔍 **数据查询**: 支持多条件筛选查询历史修改记录
- 📊 **统计分析**: 提供修改频率、语言分布等统计信息
- 💾 **本地存储**: 使用JSON文件进行数据持久化
- 🔒 **隐私保护**: 所有数据存储在本地，保护代码隐私

## 系统要求

- Python 3.8+
- [uv](https://docs.astral.sh/uv/) 包管理器

## 快速开始

```bash
# 1. 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 克隆项目
git clone <repository-url>
cd code-adjustment-tracker-mcp

# 3. 设置开发环境
uv sync

# 4. 运行服务器
uv run python main.py
```

## 安装和配置

### 1. 安装uv包管理器

```bash
# Windows
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 安装项目依赖

```bash
# 使用uv安装依赖（推荐）
uv sync
```

### 3. 配置MCP客户端

在Claude Desktop的配置文件中添加以下配置：

**Windows**: `%APPDATA%/Claude/claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

#### 方式1: 直接运行（开发模式）

```json
{
  "mcpServers": {
    "code-adjustment-tracker": {
      "command": "uv",
      "args": ["run", "python", "main.py"],
      "cwd": "/path/to/code-adjustment-tracker-mcp",
      "env": {
        "CAT_LOG_FILE": "/path/to/code-adjustment-tracker-mcp/log/app.log",
        "CAT_DATA_DIR": "./data",
        "CAT_LOG_LEVEL": "INFO",
        "CAT_MAX_QUERY_LIMIT": "100",
        "CAT_AUTO_BACKUP": "true"
      }
    }
  }
}
```

#### 方式2: 使用uvx安装wheel包（推荐生产环境）

```json
{
  "mcpServers": {
    "code-adjustment-tracker": {
      "command": "uvx",
      "args": [
        "/path/to/dist/code_adjustment_tracker_mcp-0.1.0-py3-none-any.whl"
      ],
      "env": {
        "CAT_LOG_FILE": "/path/to/logs/cat.log",
        "CAT_DATA_DIR": "/path/to/data",
        "CAT_LOG_LEVEL": "INFO"
      }
    }
  }
}
```

### 4. 启动服务器

```bash
# 使用uv运行（推荐）
uv run python main.py

# 或者直接运行
python main.py
```

## 开发工作流

### 开发环境设置

```bash
# 克隆项目
git clone <repository-url>
cd code-adjustment-tracker-mcp

# 安装uv（如果还没有安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 同步依赖
uv sync

# 运行开发服务器
uv run python main.py
```

### 构建和分发

```bash
# 构建wheel包
uv build

# 或者使用构建脚本
uv run python build.py

# 安装本地构建的包
uvx ./dist/code_adjustment_tracker_mcp-0.1.0-py3-none-any.whl
```

### 代码质量检查

```bash
# 格式化代码
uv run black .
uv run isort .

# 类型检查
uv run mypy .

# 运行测试
uv run pytest
```

### 使用开发脚本（推荐）

```bash
# 设置开发环境
python scripts/dev.py setup

# 格式化代码
python scripts/dev.py format

# 代码检查
python scripts/dev.py lint

# 运行测试
python scripts/dev.py test

# 构建项目
python scripts/dev.py build

# 清理构建文件
python scripts/dev.py clean
```

## 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `CAT_LOG_FILE` | `./log/app.log` | 日志文件路径 |
| `CAT_DATA_DIR` | `./data` | 数据存储目录 |
| `CAT_LOG_LEVEL` | `INFO` | 日志级别 (DEBUG/INFO/WARNING/ERROR) |
| `CAT_MAX_QUERY_LIMIT` | `100` | 查询返回的最大记录数 |
| `CAT_DEFAULT_QUERY_LIMIT` | `10` | 默认查询记录数 |
| `CAT_CACHE_TTL` | `24` | 缓存有效期（小时） |
| `CAT_AUTO_BACKUP` | `true` | 是否自动备份数据 |
| `CAT_BACKUP_INTERVAL` | `7` | 备份间隔（天） |
| `CAT_MASK_ERROR_DETAILS` | `false` | 是否隐藏错误详情 |

## 使用方法

### 收集代码修改数据

```
使用collect_code_adjustment工具记录代码修改：

参数：
- ai_generated_code: AI生成的原始代码
- human_adjusted_code: 人工修改后的代码  
- adjustment_reason: 修改理由
- adjuster: 修改人姓名
- project_name: 项目名称（可选）
- programming_language: 编程语言（可选）
- model_name: AI模型名称（可选）
- coding_tool: 编码工具（可选）
```

### 查询修改记录

```
使用query_adjustments工具查询历史记录：

参数：
- start_date: 开始日期（可选）
- end_date: 结束日期（可选）
- project_name: 项目名称（可选）
- programming_language: 编程语言（可选）
- adjuster: 修改人（可选）
- limit: 返回记录数（默认10）
```

### 获取统计信息

```
使用get_adjustment_stats工具获取统计信息：

返回：
- 总修改次数
- 涉及项目数
- 参与人数
- 编程语言分布
- 活跃修改者排行
```

### 导出数据

```
使用export_adjustments工具导出数据：

参数：
- format: 导出格式（"json" 或 "csv"）
- start_date: 开始日期（可选）
- end_date: 结束日期（可选）
- project_name: 项目名称（可选）
- programming_language: 编程语言（可选）

示例：
export_adjustments(format="csv", programming_language="python")
```

## 数据结构

每条代码修改记录包含以下字段：

```json
{
  "ai_generated_code": "AI生成的代码",
  "human_adjusted_code": "修改后的代码",
  "adjustment_reason": "修改理由",
  "adjuster": "修改人姓名",
  "adjustment_time": "2023-07-15T14:45:22Z",
  "project_name": "项目名称",
  "programming_language": "编程语言",
  "model_name": "AI模型",
  "coding_tool": "编码工具"
}
```

## 项目结构

```
code-adjustment-tracker-mcp/
├── main.py                # MCP服务器主文件
├── __init__.py
├── config/
│   ├── __init__.py
│   └── settings.py        # 配置管理
├── models/
│   ├── __init__.py
│   └── adjustment.py      # 数据模型定义
├── services/
│   ├── __init__.py
│   └── adjustment_manager.py  # 业务逻辑
├── storage/
│   ├── __init__.py
│   └── file_manager.py    # 文件管理
├── data/
│   └── adjustments.json   # 数据存储文件
├── log/
│   └── app.log           # 日志文件
├── scripts/
│   └── dev.py            # 开发辅助脚本
├── pyproject.toml
├── uv.lock              # uv依赖锁定文件
└── README.md
```

### 核心工具规格
- **collect_code_adjustment**: 收集代码修改数据
- **query_adjustments**: 查询历史修改记录
- **get_adjustment_stats**: 获取统计信息
- **export_adjustments**: 导出数据（支持JSON/CSV格式）

## 开发计划

- [x] 核心数据收集功能
- [x] 查询和统计功能
- [x] 数据导出功能
- [ ] Git集成自动检测
- [ ] Web界面
- [ ] 高级分析功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！
