"""
应用程序配置设置

定义应用程序的配置参数和环境变量。
"""

import os
from pathlib import Path
from typing import Optional


class Config:
    """应用程序配置类"""
    
    def __init__(self):
        """初始化配置"""
        # 基本信息
        self.name = "CodeAdjustmentTracker"
        self.version = "0.1.0"
        self.description = "AI代码修改数据收集工具"

        # 目录配置
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = Path(os.getenv("CAT_DATA_DIR", self.base_dir / "data"))
        self.log_dir = self.base_dir / "log"

        # 确保目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 文件路径
        self.data_file = self.data_dir / "adjustments.json"
        self.log_file = Path(os.getenv("CAT_LOG_FILE", self.log_dir / "app.log"))

        # 日志配置
        self.log_level = os.getenv("CAT_LOG_LEVEL", "INFO")

        # 数据配置
        self.max_query_limit = int(os.getenv("CAT_MAX_QUERY_LIMIT", "100"))
        self.default_query_limit = int(os.getenv("CAT_DEFAULT_QUERY_LIMIT", "10"))
        self.cache_ttl = int(os.getenv("CAT_CACHE_TTL", "24"))  # 小时

        # 安全配置
        self.mask_error_details = os.getenv("CAT_MASK_ERROR_DETAILS", "false").lower() == "true"

        # 备份配置
        self.auto_backup = os.getenv("CAT_AUTO_BACKUP", "true").lower() == "true"
        self.backup_interval = int(os.getenv("CAT_BACKUP_INTERVAL", "7"))  # 天
    
    def should_log_api_details(self) -> bool:
        """是否应该记录API详细信息"""
        return self.log_level.upper() in ["DEBUG", "INFO"]
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """截断文本到指定长度"""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."


# 创建全局配置实例
config = Config()
