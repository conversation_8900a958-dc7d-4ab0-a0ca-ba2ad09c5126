[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "code-adjustment-tracker-mcp"
version = "0.1.0"
description = "AI代码修改数据收集工具 - MCP服务器"
authors = [
    {name = "AI Assistant", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "fastmcp>=2.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
code-adjustment-tracker = "main:main"

[project.urls]
Homepage = "https://github.com/your-org/code-adjustment-tracker-mcp"
Repository = "https://github.com/your-org/code-adjustment-tracker-mcp"
Issues = "https://github.com/your-org/code-adjustment-tracker-mcp/issues"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.uv]
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0",
    "build>=0.10.0",
]

[tool.uv.sources]
# 可以在这里指定特定的包源
