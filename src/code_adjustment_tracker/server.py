"""
MCP服务器主文件

实现AI代码修改数据收集的MCP服务器。
"""

import asyncio
import logging
from typing import Any, Sequence

from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
import mcp.server.stdio
import mcp.types as types

from .storage import AdjustmentStorage
from .tools import AdjustmentTools

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("code-adjustment-tracker")

# 创建服务器实例
server = Server("code-adjustment-tracker")

# 初始化存储和工具
storage = AdjustmentStorage()
tools = AdjustmentTools(storage)


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """
    列出可用的工具。
    每个工具都有名称、描述和JSON Schema定义的输入参数。
    """
    return tools.get_tools()


@server.call_tool()
async def handle_call_tool(
    name: str, arguments: dict[str, Any] | None
) -> list[types.TextContent | types.ImageContent | types.EmbeddedResource]:
    """
    处理工具调用请求。
    根据工具名称分发到相应的处理函数。
    """
    if arguments is None:
        arguments = {}

    if name == "collect_code_adjustment":
        return await tools.handle_collect_code_adjustment(arguments)
    elif name == "query_adjustments":
        return await tools.handle_query_adjustments(arguments)
    elif name == "get_adjustment_stats":
        return await tools.handle_get_adjustment_stats(arguments)
    else:
        raise ValueError(f"未知工具: {name}")


async def main():
    """主函数：启动MCP服务器"""
    # 使用stdio传输运行服务器
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="code-adjustment-tracker",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


if __name__ == "__main__":
    """程序入口点"""
    asyncio.run(main())
