"""
MCP工具实现

实现代码修改数据收集的各种工具。
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from mcp.server.models import Tool
from mcp.types import TextContent, EmbeddedResource

from .models import CodeAdjustment, AdjustmentQuery
from .storage import AdjustmentStorage


class AdjustmentTools:
    """代码修改工具集合"""
    
    def __init__(self, storage: AdjustmentStorage):
        """初始化工具集合
        
        Args:
            storage: 存储管理器
        """
        self.storage = storage
    
    def get_tools(self) -> List[Tool]:
        """获取所有可用工具
        
        Returns:
            List[Tool]: 工具列表
        """
        return [
            Tool(
                name="collect_code_adjustment",
                description="收集AI代码修改数据",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "ai_generated_code": {
                            "type": "string",
                            "description": "AI生成的原始代码"
                        },
                        "human_adjusted_code": {
                            "type": "string",
                            "description": "人工修改后的代码"
                        },
                        "adjustment_reason": {
                            "type": "string",
                            "description": "修改理由"
                        },
                        "adjuster": {
                            "type": "string",
                            "description": "修改人姓名"
                        },
                        "project_name": {
                            "type": "string",
                            "description": "项目名称（可选）"
                        },
                        "programming_language": {
                            "type": "string",
                            "description": "编程语言（可选）"
                        },
                        "model_name": {
                            "type": "string",
                            "description": "使用的AI模型（可选）"
                        },
                        "coding_tool": {
                            "type": "string",
                            "description": "编码工具名称（可选）"
                        }
                    },
                    "required": ["ai_generated_code", "human_adjusted_code", "adjustment_reason", "adjuster"]
                }
            ),
            Tool(
                name="query_adjustments",
                description="查询代码修改记录",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "start_date": {
                            "type": "string",
                            "description": "开始日期 (ISO格式，可选)"
                        },
                        "end_date": {
                            "type": "string",
                            "description": "结束日期 (ISO格式，可选)"
                        },
                        "project_name": {
                            "type": "string",
                            "description": "项目名称（可选）"
                        },
                        "programming_language": {
                            "type": "string",
                            "description": "编程语言（可选）"
                        },
                        "adjuster": {
                            "type": "string",
                            "description": "修改人（可选）"
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回记录数限制（默认10，最大100）",
                            "minimum": 1,
                            "maximum": 100
                        }
                    },
                    "required": []
                }
            ),
            Tool(
                name="get_adjustment_stats",
                description="获取代码修改统计信息",
                inputSchema={
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            )
        ]
    
    async def handle_collect_code_adjustment(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理代码修改收集请求"""
        try:
            # 创建代码修改记录
            adjustment = CodeAdjustment(
                ai_generated_code=arguments["ai_generated_code"],
                human_adjusted_code=arguments["human_adjusted_code"],
                adjustment_reason=arguments["adjustment_reason"],
                adjuster=arguments["adjuster"],
                project_name=arguments.get("project_name"),
                programming_language=arguments.get("programming_language"),
                model_name=arguments.get("model_name"),
                coding_tool=arguments.get("coding_tool")
            )
            
            # 保存到存储
            success = self.storage.save_adjustment(adjustment)
            
            if success:
                return [TextContent(
                    type="text",
                    text=f"✅ 代码修改记录已成功保存！\n\n"
                         f"📝 修改理由: {adjustment.adjustment_reason}\n"
                         f"👤 修改人: {adjustment.adjuster}\n"
                         f"⏰ 记录时间: {adjustment.adjustment_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                         f"🔧 编程语言: {adjustment.programming_language or '未指定'}\n"
                         f"📁 项目: {adjustment.project_name or '未指定'}"
                )]
            else:
                return [TextContent(
                    type="text",
                    text="❌ 保存代码修改记录失败，请检查数据格式和存储权限。"
                )]
                
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"❌ 处理请求时发生错误: {str(e)}"
            )]
    
    async def handle_query_adjustments(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理查询请求"""
        try:
            # 解析查询条件
            query = AdjustmentQuery(
                start_date=datetime.fromisoformat(arguments["start_date"]) if arguments.get("start_date") else None,
                end_date=datetime.fromisoformat(arguments["end_date"]) if arguments.get("end_date") else None,
                project_name=arguments.get("project_name"),
                programming_language=arguments.get("programming_language"),
                adjuster=arguments.get("adjuster"),
                limit=arguments.get("limit", 10)
            )
            
            # 执行查询
            results = self.storage.query_adjustments(query)
            
            if not results:
                return [TextContent(
                    type="text",
                    text="📭 没有找到匹配的代码修改记录。"
                )]
            
            # 格式化结果
            response_text = f"📊 找到 {len(results)} 条代码修改记录：\n\n"
            
            for i, adjustment in enumerate(results, 1):
                response_text += f"**记录 {i}:**\n"
                response_text += f"👤 修改人: {adjustment.adjuster}\n"
                response_text += f"📝 修改理由: {adjustment.adjustment_reason}\n"
                response_text += f"⏰ 时间: {adjustment.adjustment_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                response_text += f"🔧 语言: {adjustment.programming_language or '未指定'}\n"
                response_text += f"📁 项目: {adjustment.project_name or '未指定'}\n"
                response_text += "---\n"
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"❌ 查询时发生错误: {str(e)}"
            )]
    
    async def handle_get_adjustment_stats(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理统计信息请求"""
        try:
            stats = self.storage.get_stats()
            
            response_text = "📈 **代码修改统计信息**\n\n"
            response_text += f"📊 总修改次数: {stats.total_adjustments}\n"
            response_text += f"📁 涉及项目数: {stats.unique_projects}\n"
            response_text += f"👥 参与人数: {stats.unique_adjusters}\n\n"
            
            if stats.language_distribution:
                response_text += "🔧 **编程语言分布:**\n"
                for lang, count in sorted(stats.language_distribution.items(), key=lambda x: x[1], reverse=True):
                    response_text += f"  • {lang}: {count} 次\n"
                response_text += "\n"
            
            if stats.top_adjusters:
                response_text += "🏆 **活跃修改者:**\n"
                for adjuster in stats.top_adjusters:
                    response_text += f"  • {adjuster['name']}: {adjuster['count']} 次\n"
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"❌ 获取统计信息时发生错误: {str(e)}"
            )]
