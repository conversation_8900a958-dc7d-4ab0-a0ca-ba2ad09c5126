"""
数据模型定义

定义代码修改数据的结构和验证逻辑。
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator


class CodeAdjustment(BaseModel):
    """代码修改记录模型"""
    
    # 必需字段
    ai_generated_code: str = Field(..., description="AI生成的原始代码")
    human_adjusted_code: str = Field(..., description="人工修改后的代码")
    adjustment_reason: str = Field(..., description="修改理由")
    adjuster: str = Field(..., description="修改人姓名")
    adjustment_time: datetime = Field(default_factory=datetime.now, description="修改时间")
    
    # 可选字段
    project_name: Optional[str] = Field(None, description="项目名称")
    programming_language: Optional[str] = Field(None, description="编程语言")
    model_name: Optional[str] = Field(None, description="使用的AI模型")
    coding_tool: Optional[str] = Field(None, description="编码工具名称")
    
    @validator('ai_generated_code', 'human_adjusted_code', 'adjustment_reason', 'adjuster')
    def validate_non_empty_strings(cls, v: str) -> str:
        """验证必需字符串字段不为空"""
        if not v or not v.strip():
            raise ValueError("字段不能为空")
        return v.strip()
    
    @validator('programming_language')
    def validate_programming_language(cls, v: Optional[str]) -> Optional[str]:
        """验证编程语言格式"""
        if v is not None:
            return v.lower().strip()
        return v
    
    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AdjustmentQuery(BaseModel):
    """查询条件模型"""
    
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    project_name: Optional[str] = Field(None, description="项目名称")
    programming_language: Optional[str] = Field(None, description="编程语言")
    adjuster: Optional[str] = Field(None, description="修改人")
    limit: Optional[int] = Field(10, ge=1, le=100, description="返回记录数限制")
    
    @validator('programming_language')
    def validate_programming_language(cls, v: Optional[str]) -> Optional[str]:
        """验证编程语言格式"""
        if v is not None:
            return v.lower().strip()
        return v


class AdjustmentStats(BaseModel):
    """统计信息模型"""
    
    total_adjustments: int = Field(..., description="总修改次数")
    unique_projects: int = Field(..., description="涉及项目数")
    unique_adjusters: int = Field(..., description="参与修改人数")
    language_distribution: dict[str, int] = Field(..., description="编程语言分布")
    recent_activity: list[dict] = Field(..., description="最近活动")
    top_adjusters: list[dict] = Field(..., description="活跃修改者")
