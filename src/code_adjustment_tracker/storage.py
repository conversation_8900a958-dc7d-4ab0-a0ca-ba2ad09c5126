"""
数据存储层

处理代码修改数据的本地存储和检索。
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional
from .models import CodeAdjustment, AdjustmentQuery, AdjustmentStats


class AdjustmentStorage:
    """代码修改数据存储管理器"""
    
    def __init__(self, data_dir: str = "data"):
        """初始化存储管理器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.data_file = self.data_dir / "adjustments.json"
        self._ensure_data_file()
    
    def _ensure_data_file(self) -> None:
        """确保数据文件存在"""
        if not self.data_file.exists():
            self._save_data([])
    
    def _load_data(self) -> List[dict]:
        """加载所有数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_data(self, data: List[dict]) -> None:
        """保存数据到文件"""
        with open(self.data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
    
    def save_adjustment(self, adjustment: CodeAdjustment) -> bool:
        """保存代码修改记录
        
        Args:
            adjustment: 代码修改记录
            
        Returns:
            bool: 保存是否成功
        """
        try:
            data = self._load_data()
            adjustment_dict = adjustment.dict()
            # 确保时间格式正确
            adjustment_dict['adjustment_time'] = adjustment.adjustment_time.isoformat()
            data.append(adjustment_dict)
            self._save_data(data)
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def query_adjustments(self, query: AdjustmentQuery) -> List[CodeAdjustment]:
        """查询代码修改记录
        
        Args:
            query: 查询条件
            
        Returns:
            List[CodeAdjustment]: 匹配的记录列表
        """
        data = self._load_data()
        results = []
        
        for item in data:
            # 时间过滤
            if query.start_date or query.end_date:
                item_time = datetime.fromisoformat(item['adjustment_time'])
                if query.start_date and item_time < query.start_date:
                    continue
                if query.end_date and item_time > query.end_date:
                    continue
            
            # 项目名称过滤
            if query.project_name and item.get('project_name') != query.project_name:
                continue
            
            # 编程语言过滤
            if query.programming_language and item.get('programming_language') != query.programming_language:
                continue
            
            # 修改人过滤
            if query.adjuster and item.get('adjuster') != query.adjuster:
                continue
            
            try:
                results.append(CodeAdjustment(**item))
            except Exception:
                continue  # 跳过无效数据
        
        # 按时间倒序排列并限制数量
        results.sort(key=lambda x: x.adjustment_time, reverse=True)
        return results[:query.limit]
    
    def get_stats(self) -> AdjustmentStats:
        """获取统计信息
        
        Returns:
            AdjustmentStats: 统计信息
        """
        data = self._load_data()
        
        if not data:
            return AdjustmentStats(
                total_adjustments=0,
                unique_projects=0,
                unique_adjusters=0,
                language_distribution={},
                recent_activity=[],
                top_adjusters=[]
            )
        
        # 基本统计
        total_adjustments = len(data)
        unique_projects = len(set(item.get('project_name') for item in data if item.get('project_name')))
        unique_adjusters = len(set(item.get('adjuster') for item in data if item.get('adjuster')))
        
        # 编程语言分布
        language_distribution = {}
        for item in data:
            lang = item.get('programming_language')
            if lang:
                language_distribution[lang] = language_distribution.get(lang, 0) + 1
        
        # 最近活动（最近7天）
        recent_activity = []
        adjuster_counts = {}
        
        for item in data:
            adjuster = item.get('adjuster')
            if adjuster:
                adjuster_counts[adjuster] = adjuster_counts.get(adjuster, 0) + 1
        
        # 活跃修改者排序
        top_adjusters = [
            {"name": name, "count": count}
            for name, count in sorted(adjuster_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        ]
        
        return AdjustmentStats(
            total_adjustments=total_adjustments,
            unique_projects=unique_projects,
            unique_adjusters=unique_adjusters,
            language_distribution=language_distribution,
            recent_activity=recent_activity,
            top_adjusters=top_adjusters
        )
