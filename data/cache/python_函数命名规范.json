{"metadata": {"selector": {"language": "python", "framework": null, "version": null, "question": "函数命名规范"}, "fetch_date": "2025-07-18 11:09:05.484149", "source_url": "http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat", "content_hash": "dfe50837aefaf25051b7e00d31668244"}, "content": "在Python编程语言中，函数命名规范建议使用小写字母，单词之间用下划线连接，如 `get_user_by_id`。函数名应清晰表达其功能，并在函数定义时添加相应的文档字符串（docstring）以说明函数的用途、参数和返回值。此外，Python社区普遍遵循PEP 8风格指南，该指南推荐函数名应简洁明了，避免使用复杂的表达方式。对于超过5个参数的函数，建议使用数据类或字典来传递参数，以保持函数接口的清晰性。单个函数长度不宜过长，超过100行的函数应考虑拆分，以提高代码的可读性和可维护性。"}