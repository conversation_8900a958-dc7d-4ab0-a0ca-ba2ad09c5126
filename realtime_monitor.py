#!/usr/bin/env python3
"""
实时文件监控示例
使用Python内置模块实现简单的文件监控
"""

import os
import time
import threading
from datetime import datetime
from pathlib import Path

class SimpleFileMonitor:
    def __init__(self, watch_directory: str = "."):
        self.watch_directory = Path(watch_directory)
        self.file_states = {}
        self.monitoring = False
        self.monitor_thread = None
        
    def get_file_state(self, file_path: Path) -> dict:
        """获取文件状态"""
        try:
            stat = file_path.stat()
            return {
                "size": stat.st_size,
                "mtime": stat.st_mtime,
                "exists": True
            }
        except:
            return {"exists": False}
    
    def scan_directory(self) -> dict:
        """扫描目录获取所有文件状态"""
        current_states = {}
        
        for file_path in self.watch_directory.rglob("*"):
            if file_path.is_file():
                # 只监控代码文件
                if file_path.suffix in ['.py', '.js', '.ts', '.md', '.txt']:
                    relative_path = str(file_path.relative_to(self.watch_directory))
                    current_states[relative_path] = self.get_file_state(file_path)
        
        return current_states
    
    def detect_changes(self, old_states: dict, new_states: dict) -> list:
        """检测文件变化"""
        changes = []
        
        # 检测修改和新增
        for path, new_state in new_states.items():
            if path not in old_states:
                changes.append({
                    "type": "created",
                    "path": path,
                    "timestamp": datetime.now().isoformat()
                })
            elif old_states[path]["exists"] and new_state["exists"]:
                if old_states[path]["mtime"] != new_state["mtime"]:
                    changes.append({
                        "type": "modified",
                        "path": path,
                        "timestamp": datetime.now().isoformat(),
                        "old_size": old_states[path]["size"],
                        "new_size": new_state["size"]
                    })
        
        # 检测删除
        for path in old_states:
            if path not in new_states:
                changes.append({
                    "type": "deleted",
                    "path": path,
                    "timestamp": datetime.now().isoformat()
                })
        
        return changes
    
    def monitor_loop(self):
        """监控循环"""
        print(f"开始监控目录: {self.watch_directory}")
        self.file_states = self.scan_directory()
        print(f"初始扫描完成，发现 {len(self.file_states)} 个文件")
        
        while self.monitoring:
            time.sleep(2)  # 每2秒检查一次
            
            current_states = self.scan_directory()
            changes = self.detect_changes(self.file_states, current_states)
            
            for change in changes:
                self.print_change(change)
            
            self.file_states = current_states
    
    def print_change(self, change: dict):
        """打印文件变化"""
        timestamp = change["timestamp"]
        path = change["path"]
        change_type = change["type"]
        
        if change_type == "created":
            print(f"[{timestamp}] ✅ 新建: {path}")
        elif change_type == "modified":
            old_size = change["old_size"]
            new_size = change["new_size"]
            size_change = new_size - old_size
            size_indicator = "📈" if size_change > 0 else "📉" if size_change < 0 else "🔄"
            print(f"[{timestamp}] {size_indicator} 修改: {path} ({old_size} → {new_size} bytes)")
        elif change_type == "deleted":
            print(f"[{timestamp}] ❌ 删除: {path}")
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()

def demo_monitoring():
    """演示监控功能"""
    monitor = SimpleFileMonitor(".")
    
    print("=== 实时文件监控演示 ===")
    print("监控将运行10秒，请尝试修改一些文件...")
    
    monitor.start_monitoring()
    
    try:
        time.sleep(10)  # 监控10秒
    except KeyboardInterrupt:
        print("\n用户中断监控")
    
    monitor.stop_monitoring()
    print("监控已停止")

def show_current_files():
    """显示当前文件状态"""
    print("=== 当前目录文件状态 ===")
    
    for file_path in Path(".").rglob("*"):
        if file_path.is_file() and file_path.suffix in ['.py', '.md', '.txt']:
            try:
                stat = file_path.stat()
                mtime = datetime.fromtimestamp(stat.st_mtime)
                print(f"{file_path.name:20} | {stat.st_size:8} bytes | {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
            except:
                pass

if __name__ == "__main__":
    print("选择功能:")
    print("1. 显示当前文件状态")
    print("2. 启动实时监控演示")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        show_current_files()
    elif choice == "2":
        demo_monitoring()
    else:
        print("无效选择，显示当前文件状态")
        show_current_files()
