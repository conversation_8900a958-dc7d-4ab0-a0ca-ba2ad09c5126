#!/usr/bin/env python3
"""
IntelliJ IDEA workspace.xml 文件解读工具
解析和分析 IntelliJ IDEA 系列 IDE 的工作区配置文件
"""

import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

class WorkspaceXMLParser:
    """workspace.xml 文件解析器"""
    
    def __init__(self, workspace_file: str):
        self.workspace_file = Path(workspace_file)
        self.tree = None
        self.root = None
        self.parsed_data = {}
        
    def load_workspace(self) -> bool:
        """加载 workspace.xml 文件"""
        try:
            if not self.workspace_file.exists():
                print(f"文件不存在: {self.workspace_file}")
                return False
                
            self.tree = ET.parse(self.workspace_file)
            self.root = self.tree.getroot()
            return True
        except ET.ParseError as e:
            print(f"XML解析错误: {e}")
            return False
        except Exception as e:
            print(f"加载文件失败: {e}")
            return False
    
    def parse_project_info(self) -> Dict[str, Any]:
        """解析项目基本信息"""
        project_info = {
            "project_name": "Unknown",
            "project_type": "Unknown",
            "sdk_info": {},
            "modules": []
        }
        
        # 查找项目名称
        name_element = self.root.find(".//component[@name='ProjectView']/panes/pane[@id='ProjectPane']/subPane/PATH/PATH_ELEMENT")
        if name_element is not None:
            project_info["project_name"] = name_element.get("name", "Unknown")
        
        # 查找SDK信息
        sdk_element = self.root.find(".//component[@name='ProjectRootManager']")
        if sdk_element is not None:
            project_info["sdk_info"] = {
                "version": sdk_element.get("version"),
                "project_jdk_name": sdk_element.get("project-jdk-name"),
                "project_jdk_type": sdk_element.get("project-jdk-type")
            }
        
        return project_info
    
    def parse_recent_files(self) -> List[Dict[str, str]]:
        """解析最近打开的文件"""
        recent_files = []
        
        # 查找最近文件列表
        recent_manager = self.root.find(".//component[@name='RecentManager']")
        if recent_manager is not None:
            for recent_file in recent_manager.findall(".//option[@name='recentFiles']/list/option"):
                file_path = recent_file.get("value")
                if file_path:
                    recent_files.append({
                        "path": file_path,
                        "type": "recent_file"
                    })
        
        return recent_files
    
    def parse_editor_tabs(self) -> List[Dict[str, Any]]:
        """解析编辑器标签页信息"""
        editor_tabs = []
        
        # 查找文件编辑器管理器
        file_editor_manager = self.root.find(".//component[@name='FileEditorManager']")
        if file_editor_manager is not None:
            for leaf in file_editor_manager.findall(".//leaf"):
                file_element = leaf.find("file")
                if file_element is not None:
                    tab_info = {
                        "file_url": file_element.get("url"),
                        "is_selected": leaf.get("SELECTED") == "true",
                        "editors": []
                    }
                    
                    # 获取编辑器信息
                    for editor in file_element.findall("entry"):
                        editor_info = {
                            "file": editor.get("file"),
                            "provider": editor.find("provider").get("selected-editor-type-id") if editor.find("provider") is not None else None
                        }
                        tab_info["editors"].append(editor_info)
                    
                    editor_tabs.append(tab_info)
        
        return editor_tabs
    
    def parse_run_configurations(self) -> List[Dict[str, Any]]:
        """解析运行配置"""
        run_configs = []
        
        run_manager = self.root.find(".//component[@name='RunManager']")
        if run_manager is not None:
            for config in run_manager.findall("configuration"):
                config_info = {
                    "name": config.get("name"),
                    "type": config.get("type"),
                    "factory_name": config.get("factoryName"),
                    "temporary": config.get("temporary") == "true"
                }
                
                # 获取配置详细信息
                for option in config.findall("option"):
                    config_info[option.get("name")] = option.get("value")
                
                run_configs.append(config_info)
        
        return run_configs
    
    def parse_tool_windows(self) -> Dict[str, Any]:
        """解析工具窗口状态"""
        tool_windows = {}
        
        tool_window_manager = self.root.find(".//component[@name='ToolWindowManager']")
        if tool_window_manager is not None:
            for window in tool_window_manager.findall("window_info"):
                window_id = window.get("id")
                if window_id:
                    tool_windows[window_id] = {
                        "active": window.get("active") == "true",
                        "anchor": window.get("anchor"),
                        "auto_hide": window.get("auto_hide") == "true",
                        "internal_type": window.get("internal_type"),
                        "type": window.get("type"),
                        "visible": window.get("visible") == "true",
                        "weight": window.get("weight")
                    }
        
        return tool_windows
    
    def parse_vcs_info(self) -> Dict[str, Any]:
        """解析版本控制信息"""
        vcs_info = {}
        
        vcs_manager = self.root.find(".//component[@name='VcsManagerConfiguration']")
        if vcs_manager is not None:
            for option in vcs_manager.findall("option"):
                vcs_info[option.get("name")] = option.get("value")
        
        # 查找Git信息
        git_manager = self.root.find(".//component[@name='Git.Settings']")
        if git_manager is not None:
            vcs_info["git_settings"] = {}
            for option in git_manager.findall("option"):
                vcs_info["git_settings"][option.get("name")] = option.get("value")
        
        return vcs_info
    
    def parse_all(self) -> Dict[str, Any]:
        """解析所有信息"""
        if not self.load_workspace():
            return {}
        
        self.parsed_data = {
            "file_path": str(self.workspace_file),
            "parsed_at": datetime.now().isoformat(),
            "project_info": self.parse_project_info(),
            "recent_files": self.parse_recent_files(),
            "editor_tabs": self.parse_editor_tabs(),
            "run_configurations": self.parse_run_configurations(),
            "tool_windows": self.parse_tool_windows(),
            "vcs_info": self.parse_vcs_info()
        }
        
        return self.parsed_data
    
    def get_file_history_insights(self) -> Dict[str, Any]:
        """从workspace.xml提取文件修改历史洞察"""
        insights = {
            "recently_modified_files": [],
            "active_development_areas": [],
            "project_activity_summary": {}
        }
        
        # 分析最近文件
        recent_files = self.parsed_data.get("recent_files", [])
        insights["recently_modified_files"] = recent_files[:10]  # 最近10个文件
        
        # 分析活跃开发区域
        editor_tabs = self.parsed_data.get("editor_tabs", [])
        file_types = {}
        for tab in editor_tabs:
            file_url = tab.get("file_url", "")
            if file_url:
                extension = Path(file_url).suffix
                file_types[extension] = file_types.get(extension, 0) + 1
        
        insights["active_development_areas"] = [
            {"file_type": ext, "count": count} 
            for ext, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True)
        ]
        
        # 项目活动摘要
        insights["project_activity_summary"] = {
            "total_recent_files": len(recent_files),
            "open_editor_tabs": len(editor_tabs),
            "run_configurations": len(self.parsed_data.get("run_configurations", [])),
            "has_vcs": bool(self.parsed_data.get("vcs_info"))
        }
        
        return insights
    
    def print_summary(self):
        """打印解析摘要"""
        if not self.parsed_data:
            print("未找到解析数据")
            return
        
        print("=== IntelliJ IDEA Workspace 解析结果 ===\n")
        
        # 项目信息
        project_info = self.parsed_data.get("project_info", {})
        print(f"项目名称: {project_info.get('project_name')}")
        print(f"SDK信息: {project_info.get('sdk_info', {}).get('project_jdk_name', 'Unknown')}")
        
        # 最近文件
        recent_files = self.parsed_data.get("recent_files", [])
        print(f"\n最近打开文件 ({len(recent_files)}个):")
        for i, file_info in enumerate(recent_files[:5]):
            print(f"  {i+1}. {Path(file_info['path']).name}")
        
        # 编辑器标签
        editor_tabs = self.parsed_data.get("editor_tabs", [])
        print(f"\n当前编辑器标签 ({len(editor_tabs)}个):")
        for tab in editor_tabs[:5]:
            file_url = tab.get("file_url", "")
            selected = " [当前]" if tab.get("is_selected") else ""
            print(f"  • {Path(file_url).name}{selected}")
        
        # 运行配置
        run_configs = self.parsed_data.get("run_configurations", [])
        print(f"\n运行配置 ({len(run_configs)}个):")
        for config in run_configs[:3]:
            print(f"  • {config.get('name')} ({config.get('type')})")
        
        # 版本控制
        vcs_info = self.parsed_data.get("vcs_info", {})
        if vcs_info:
            print(f"\n版本控制: 已配置")
        else:
            print(f"\n版本控制: 未配置")

def create_sample_workspace():
    """创建示例 workspace.xml 文件用于演示"""
    sample_content = '''<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectRootManager" version="2" project-jdk-name="Python 3.9" project-jdk-type="Python SDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="main.py" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/main.py">
          <provider selected-editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="RecentManager">
    <option name="recentFiles">
      <list>
        <option value="file://$PROJECT_DIR$/main.py" />
        <option value="file://$PROJECT_DIR$/config/settings.py" />
        <option value="file://$PROJECT_DIR$/README.md" />
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
    </configuration>
  </component>
</project>'''
    
    sample_file = Path("sample_workspace.xml")
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    return sample_file

def main():
    """主函数演示"""
    print("=== IntelliJ IDEA workspace.xml 解读工具 ===\n")
    
    # 检查是否存在真实的workspace.xml
    possible_paths = [
        ".idea/workspace.xml",
        "../.idea/workspace.xml",
        "workspace.xml"
    ]
    
    workspace_file = None
    for path in possible_paths:
        if Path(path).exists():
            workspace_file = path
            break
    
    if not workspace_file:
        print("未找到 workspace.xml 文件，创建示例文件进行演示...")
        workspace_file = create_sample_workspace()
        print(f"已创建示例文件: {workspace_file}")
    
    # 解析文件
    parser = WorkspaceXMLParser(workspace_file)
    parsed_data = parser.parse_all()
    
    if parsed_data:
        parser.print_summary()
        
        # 获取文件历史洞察
        insights = parser.get_file_history_insights()
        print("\n=== 文件修改历史洞察 ===")
        print(f"项目活动摘要: {json.dumps(insights['project_activity_summary'], indent=2, ensure_ascii=False)}")
        
        # 保存解析结果
        output_file = Path("workspace_analysis.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(parsed_data, f, indent=2, ensure_ascii=False)
        print(f"\n详细解析结果已保存到: {output_file}")
    else:
        print("解析失败")

if __name__ == "__main__":
    main()
