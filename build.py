#!/usr/bin/env python3
"""
构建脚本

用于构建和打包CodeAdjustmentTracker MCP服务器。
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd: str, cwd: Path = None) -> bool:
    """运行命令并返回是否成功"""
    try:
        print(f"执行命令: {cmd}")
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            check=True,
            capture_output=True,
            text=True
        )
        print(f"✅ 命令执行成功")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False


def main():
    """主函数"""
    project_root = Path(__file__).parent
    
    print("🚀 开始构建 CodeAdjustmentTracker MCP 服务器")
    
    # 1. 清理之前的构建
    print("\n📦 清理之前的构建...")
    dist_dir = project_root / "dist"
    if dist_dir.exists():
        import shutil
        shutil.rmtree(dist_dir)
        print("✅ 清理完成")
    
    # 2. 构建wheel包
    print("\n🔨 构建wheel包...")
    # 优先使用uv构建，如果失败则使用传统方式
    if not run_command("uv build", project_root):
        print("⚠️ uv构建失败，尝试使用传统方式...")
        if not run_command("python -m build", project_root):
            print("❌ 构建失败")
            sys.exit(1)
    
    # 3. 检查构建结果
    print("\n🔍 检查构建结果...")
    wheel_files = list(dist_dir.glob("*.whl"))
    tar_files = list(dist_dir.glob("*.tar.gz"))
    
    if wheel_files and tar_files:
        print("✅ 构建成功！")
        print(f"Wheel文件: {wheel_files[0].name}")
        print(f"源码包: {tar_files[0].name}")
        
        # 4. 显示安装命令
        print("\n📋 安装命令:")
        print(f"pip install {wheel_files[0]}")
        print(f"或者: uvx {wheel_files[0]}")
        
        # 5. 显示MCP配置示例
        print("\n⚙️ MCP配置示例:")
        config_example = f'''{{
  "mcpServers": {{
    "code-adjustment-tracker": {{
      "command": "uvx",
      "args": ["{wheel_files[0].absolute()}"],
      "env": {{
        "CAT_LOG_FILE": "./log/cat.log",
        "CAT_DATA_DIR": "./data",
        "CAT_LOG_LEVEL": "INFO"
      }}
    }}
  }}
}}'''
        print(config_example)
        
    else:
        print("❌ 构建失败，未找到输出文件")
        sys.exit(1)


if __name__ == "__main__":
    main()
