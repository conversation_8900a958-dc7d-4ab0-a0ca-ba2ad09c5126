#!/usr/bin/env python3
"""
高级文件修改追踪工具
演示多种非git方式获取文件修改记录
"""

import os
import json
import hashlib
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

class FileTracker:
    def __init__(self, watch_directory: str = "."):
        self.watch_directory = Path(watch_directory)
        self.history_file = self.watch_directory / "file_history.json"
        self.snapshots = {}
        self.load_history()
    
    def get_file_hash(self, file_path: Path) -> str:
        """计算文件内容的MD5哈希值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except:
            return ""
    
    def get_file_info(self, file_path: Path) -> Dict:
        """获取文件的详细信息"""
        try:
            stat = file_path.stat()
            return {
                "path": str(file_path),
                "size": stat.st_size,
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "hash": self.get_file_hash(file_path),
                "exists": True
            }
        except:
            return {
                "path": str(file_path),
                "exists": False
            }
    
    def scan_files(self, extensions: List[str] = None) -> Dict[str, Dict]:
        """扫描目录中的文件"""
        if extensions is None:
            extensions = ['.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.md', '.txt']
        
        current_snapshot = {}
        
        for file_path in self.watch_directory.rglob("*"):
            if file_path.is_file() and any(file_path.suffix == ext for ext in extensions):
                # 跳过隐藏文件和特殊目录
                if any(part.startswith('.') for part in file_path.parts):
                    continue
                
                relative_path = file_path.relative_to(self.watch_directory)
                current_snapshot[str(relative_path)] = self.get_file_info(file_path)
        
        return current_snapshot
    
    def detect_changes(self, old_snapshot: Dict, new_snapshot: Dict) -> Dict:
        """检测文件变化"""
        changes = {
            "added": [],
            "modified": [],
            "deleted": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 检测新增和修改的文件
        for path, info in new_snapshot.items():
            if path not in old_snapshot:
                changes["added"].append({
                    "path": path,
                    "info": info
                })
            elif old_snapshot[path].get("hash") != info.get("hash"):
                changes["modified"].append({
                    "path": path,
                    "old_info": old_snapshot[path],
                    "new_info": info
                })
        
        # 检测删除的文件
        for path in old_snapshot:
            if path not in new_snapshot:
                changes["deleted"].append({
                    "path": path,
                    "info": old_snapshot[path]
                })
        
        return changes
    
    def load_history(self):
        """加载历史记录"""
        try:
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.snapshots = data.get("snapshots", {})
        except:
            self.snapshots = {}
    
    def save_history(self):
        """保存历史记录"""
        try:
            data = {
                "snapshots": self.snapshots,
                "last_updated": datetime.now().isoformat()
            }
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存历史记录失败: {e}")
    
    def take_snapshot(self) -> str:
        """创建当前状态快照"""
        timestamp = datetime.now().isoformat()
        current_snapshot = self.scan_files()
        
        # 如果有之前的快照，检测变化
        if self.snapshots:
            last_snapshot_key = max(self.snapshots.keys())
            last_snapshot = self.snapshots[last_snapshot_key]["files"]
            changes = self.detect_changes(last_snapshot, current_snapshot)
            
            if any(changes[key] for key in ["added", "modified", "deleted"]):
                self.snapshots[timestamp] = {
                    "files": current_snapshot,
                    "changes": changes
                }
                print(f"检测到变化，创建快照: {timestamp}")
                return timestamp
            else:
                print("未检测到文件变化")
                return None
        else:
            # 第一次快照
            self.snapshots[timestamp] = {
                "files": current_snapshot,
                "changes": {"added": list(current_snapshot.values()), "modified": [], "deleted": []}
            }
            print(f"创建初始快照: {timestamp}")
            return timestamp
    
    def get_file_history(self, file_path: str) -> List[Dict]:
        """获取特定文件的修改历史"""
        history = []
        
        for timestamp, snapshot in self.snapshots.items():
            if file_path in snapshot["files"]:
                history.append({
                    "timestamp": timestamp,
                    "info": snapshot["files"][file_path]
                })
        
        return history
    
    def print_changes_summary(self):
        """打印变化摘要"""
        if not self.snapshots:
            print("暂无快照记录")
            return
        
        print("\n=== 文件修改历史摘要 ===")
        for timestamp, snapshot in sorted(self.snapshots.items()):
            changes = snapshot["changes"]
            print(f"\n时间: {timestamp}")
            
            if changes["added"]:
                print(f"  新增文件 ({len(changes['added'])}个):")
                for item in changes["added"][:3]:  # 只显示前3个
                    print(f"    + {item['path']}")
                if len(changes["added"]) > 3:
                    print(f"    ... 还有 {len(changes['added']) - 3} 个文件")
            
            if changes["modified"]:
                print(f"  修改文件 ({len(changes['modified'])}个):")
                for item in changes["modified"][:3]:
                    print(f"    ~ {item['path']}")
                if len(changes["modified"]) > 3:
                    print(f"    ... 还有 {len(changes['modified']) - 3} 个文件")
            
            if changes["deleted"]:
                print(f"  删除文件 ({len(changes['deleted'])}个):")
                for item in changes["deleted"][:3]:
                    print(f"    - {item['path']}")

def main():
    """主函数演示"""
    tracker = FileTracker(".")
    
    print("=== 高级文件修改追踪工具 ===")
    print("正在扫描当前目录...")
    
    # 创建快照
    snapshot_id = tracker.take_snapshot()
    
    if snapshot_id:
        tracker.save_history()
        print("快照已保存")
    
    # 显示历史摘要
    tracker.print_changes_summary()
    
    # 显示特定文件的历史
    test_files = ["main.py", "README.md"]
    for file_path in test_files:
        history = tracker.get_file_history(file_path)
        if history:
            print(f"\n=== {file_path} 的修改历史 ===")
            for record in history[-3:]:  # 显示最近3次
                print(f"  {record['timestamp']}: {record['info']['size']} bytes")

if __name__ == "__main__":
    main()
