# This file is automatically generated by uv.
# To update, run `uv lock`.

version = 1
requires-python = ">=3.8"

[[package]]
name = "code-adjustment-tracker-mcp"
version = "0.1.0"
source = { editable = "." }
dependencies = [
    { name = "fastmcp" },
    { name = "pydantic" },
    { name = "typing-extensions" },
]

[package.metadata]
requires-dist = [
    "fastmcp>=2.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
]

[[package]]
name = "fastmcp"
version = "2.0.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "pydantic" },
]

[[package]]
name = "pydantic"
version = "2.5.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "typing-extensions" },
]

[[package]]
name = "typing-extensions"
version = "4.8.0"
source = { registry = "https://pypi.org/simple" }
