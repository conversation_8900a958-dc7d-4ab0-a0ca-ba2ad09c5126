"""
代码修改管理服务

提供代码修改数据的保存、查询和统计功能。
"""

import csv
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from io import StringIO

from config.settings import config
from models.adjustment import CodeAdjustment, AdjustmentQuery, AdjustmentStats
from storage.file_manager import file_manager

logger = logging.getLogger(__name__)


class AdjustmentManager:
    """代码修改管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.data_file = config.data_file
        self._ensure_data_file()
    
    def _ensure_data_file(self) -> None:
        """确保数据文件存在"""
        if not self.data_file.exists():
            self._save_data([])
    
    def _load_data(self) -> List[Dict[str, Any]]:
        """加载所有数据"""
        try:
            return file_manager.load_json(self.data_file)
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return []
    
    def _save_data(self, data: List[Dict[str, Any]]) -> None:
        """保存数据到文件"""
        try:
            file_manager.save_json(self.data_file, data)
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
            raise
    
    async def save_adjustment(self, adjustment: CodeAdjustment) -> Dict[str, Any]:
        """保存代码修改记录
        
        Args:
            adjustment: 代码修改记录
            
        Returns:
            Dict[str, Any]: 保存结果
        """
        try:
            data = self._load_data()
            adjustment_dict = adjustment.dict()
            # 确保时间格式正确
            adjustment_dict['adjustment_time'] = adjustment.adjustment_time.isoformat()
            data.append(adjustment_dict)
            self._save_data(data)
            
            return {
                "success": True,
                "message": "代码修改记录已成功保存",
                "adjustment_id": len(data),
                "adjustment_time": adjustment.adjustment_time.isoformat(),
                "adjuster": adjustment.adjuster,
                "programming_language": adjustment.programming_language,
                "project_name": adjustment.project_name
            }
        except Exception as e:
            logger.error(f"保存代码修改记录失败: {e}")
            return {
                "success": False,
                "error": f"保存失败: {str(e)}"
            }
    
    async def query_adjustments(self, query: AdjustmentQuery) -> Dict[str, Any]:
        """查询代码修改记录
        
        Args:
            query: 查询条件
            
        Returns:
            Dict[str, Any]: 查询结果
        """
        try:
            data = self._load_data()
            results = []
            
            for item in data:
                # 时间过滤
                if query.start_date or query.end_date:
                    try:
                        item_time = datetime.fromisoformat(item['adjustment_time'])
                        if query.start_date and item_time < query.start_date:
                            continue
                        if query.end_date and item_time > query.end_date:
                            continue
                    except (ValueError, KeyError):
                        continue
                
                # 项目名称过滤
                if query.project_name and item.get('project_name') != query.project_name:
                    continue
                
                # 编程语言过滤
                if query.programming_language and item.get('programming_language') != query.programming_language:
                    continue
                
                # 修改人过滤
                if query.adjuster and item.get('adjuster') != query.adjuster:
                    continue
                
                results.append(item)
            
            # 按时间倒序排列并限制数量
            results.sort(key=lambda x: x.get('adjustment_time', ''), reverse=True)
            limited_results = results[:query.limit]
            
            return {
                "success": True,
                "total_found": len(results),
                "returned_count": len(limited_results),
                "adjustments": limited_results,
                "query_conditions": {
                    "start_date": query.start_date.isoformat() if query.start_date else None,
                    "end_date": query.end_date.isoformat() if query.end_date else None,
                    "project_name": query.project_name,
                    "programming_language": query.programming_language,
                    "adjuster": query.adjuster,
                    "limit": query.limit
                }
            }
        except Exception as e:
            logger.error(f"查询代码修改记录失败: {e}")
            return {
                "success": False,
                "error": f"查询失败: {str(e)}"
            }
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            data = self._load_data()
            
            if not data:
                return {
                    "success": True,
                    "total_adjustments": 0,
                    "unique_projects": 0,
                    "unique_adjusters": 0,
                    "language_distribution": {},
                    "recent_activity": [],
                    "top_adjusters": []
                }
            
            # 基本统计
            total_adjustments = len(data)
            unique_projects = len(set(item.get('project_name') for item in data if item.get('project_name')))
            unique_adjusters = len(set(item.get('adjuster') for item in data if item.get('adjuster')))
            
            # 编程语言分布
            language_distribution = {}
            for item in data:
                lang = item.get('programming_language')
                if lang:
                    language_distribution[lang] = language_distribution.get(lang, 0) + 1
            
            # 最近7天活动
            recent_activity = []
            now = datetime.now()
            for i in range(7):
                date = now - timedelta(days=i)
                date_str = date.strftime('%Y-%m-%d')
                count = sum(1 for item in data 
                           if item.get('adjustment_time', '').startswith(date_str))
                if count > 0:
                    recent_activity.append({"date": date_str, "count": count})
            
            # 活跃修改者排序
            adjuster_counts = {}
            for item in data:
                adjuster = item.get('adjuster')
                if adjuster:
                    adjuster_counts[adjuster] = adjuster_counts.get(adjuster, 0) + 1
            
            top_adjusters = [
                {"name": name, "count": count}
                for name, count in sorted(adjuster_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            ]
            
            return {
                "success": True,
                "total_adjustments": total_adjustments,
                "unique_projects": unique_projects,
                "unique_adjusters": unique_adjusters,
                "language_distribution": language_distribution,
                "recent_activity": recent_activity,
                "top_adjusters": top_adjusters,
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                "success": False,
                "error": f"获取统计信息失败: {str(e)}"
            }

    async def export_adjustments(self, query: AdjustmentQuery, format: str = "json") -> Dict[str, Any]:
        """导出代码修改数据

        Args:
            query: 查询条件
            format: 导出格式 ("json", "csv")

        Returns:
            Dict[str, Any]: 导出结果
        """
        try:
            # 获取数据
            query_result = await self.query_adjustments(query)

            if not query_result.get("success"):
                return query_result

            adjustments = query_result.get("adjustments", [])

            if format.lower() == "csv":
                return self._export_to_csv(adjustments)
            else:
                return self._export_to_json(adjustments)

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return {
                "success": False,
                "error": f"导出失败: {str(e)}"
            }

    def _export_to_json(self, adjustments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导出为JSON格式"""
        export_data = {
            "export_info": {
                "format": "json",
                "exported_at": datetime.now().isoformat(),
                "total_records": len(adjustments),
                "tool_version": "0.1.0"
            },
            "adjustments": adjustments
        }

        # 保存到文件
        export_file = self.data_file.parent / f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        file_manager.save_json(export_file, export_data)

        return {
            "success": True,
            "message": f"数据已导出为JSON格式",
            "export_file": str(export_file),
            "total_records": len(adjustments),
            "format": "json"
        }

    def _export_to_csv(self, adjustments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """导出为CSV格式"""
        if not adjustments:
            return {
                "success": True,
                "message": "没有数据可导出",
                "total_records": 0,
                "format": "csv"
            }

        # 准备CSV数据
        output = StringIO()
        fieldnames = [
            'adjustment_time', 'adjuster', 'programming_language', 'project_name',
            'adjustment_reason', 'ai_generated_code', 'human_adjusted_code',
            'model_name', 'coding_tool'
        ]

        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for adjustment in adjustments:
            # 处理可能的None值和长文本
            row = {}
            for field in fieldnames:
                value = adjustment.get(field, '')
                if value is None:
                    value = ''
                # 限制代码字段长度以适应CSV
                if field in ['ai_generated_code', 'human_adjusted_code'] and len(str(value)) > 500:
                    value = str(value)[:500] + "..."
                row[field] = value
            writer.writerow(row)

        # 保存到文件
        export_file = self.data_file.parent / f"export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        with open(export_file, 'w', encoding='utf-8', newline='') as f:
            f.write(output.getvalue())

        return {
            "success": True,
            "message": f"数据已导出为CSV格式",
            "export_file": str(export_file),
            "total_records": len(adjustments),
            "format": "csv"
        }


# 创建全局实例
adjustment_manager = AdjustmentManager()
