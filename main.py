#!/usr/bin/env python3
"""
CodeAdjustmentTracker - AI代码修改数据收集工具

一个基于FastMCP 2.0的MCP服务器，用于收集和分析开发者对AI生成代码的修改行为。
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# FastMCP 2.0 导入
from fastmcp import FastMCP

# 导入核心模块
from config.settings import config
from models.adjustment import CodeAdjustment, AdjustmentQuery
from services.adjustment_manager import adjustment_manager


def create_error_response(
    error_type: str, message: str, **context
) -> Dict[str, Any]:
    """创建规范化的错误响应"""
    return {
        "success": False,
        "error": {
            "type": error_type,
            "message": message,
            "context": context,
        },
        "timestamp": datetime.now().isoformat(),
    }


# 配置日志
log_file_path = config.log_file or "log/app.log"

# 确保日志目录存在
import os
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file_path, encoding='utf-8'),
    ],
)

logger = logging.getLogger("CodeAdjustmentTracker")


# 创建FastMCP应用实例
mcp = FastMCP(
    name=config.name,
    instructions="""
    CodeAdjustmentTracker - AI代码修改数据收集工具

    这是一个AI代码修改数据收集MCP服务器，提供以下核心功能：

    📝 代码修改收集：
    - collect_code_adjustment: 收集AI代码修改数据

    🔍 数据查询分析：
    - query_adjustments: 查询历史修改记录
    - get_adjustment_stats: 获取统计信息

    🛠️ 数据管理：
    - export_adjustments: 导出数据
    """,
    dependencies=[
        "pydantic>=2.0.0",
        "typing-extensions>=4.0.0",
    ],
)


# ==================== MCP 工具实现 ====================

@mcp.tool(tags={"public", "collect", "data"})
async def collect_code_adjustment(
    ai_generated_code: str,
    human_adjusted_code: str,
    adjustment_reason: str,
    adjuster: str,
    project_name: Optional[str] = None,
    programming_language: Optional[str] = None,
    model_name: Optional[str] = None,
    coding_tool: Optional[str] = None,
) -> str:
    """
    收集AI代码修改数据

    记录开发者对AI生成代码的修改行为，包括原始代码、修改后代码、修改理由等信息。

    Args:
        ai_generated_code: AI生成的原始代码
        human_adjusted_code: 人工修改后的代码
        adjustment_reason: 修改理由
        adjuster: 修改人姓名
        project_name: 项目名称（可选）
        programming_language: 编程语言（可选）
        model_name: 使用的AI模型（可选）
        coding_tool: 编码工具名称（可选）

    Returns:
        JSON格式的字符串，包含保存结果

    Examples:
        collect_code_adjustment(
            ai_generated_code="def hello(): print('Hello')",
            human_adjusted_code="def hello(): print('Hello, World!')",
            adjustment_reason="添加更完整的问候语",
            adjuster="张三",
            programming_language="python"
        )
    """
    request_id = f"collect_{hash(str((ai_generated_code[:50], adjuster)))}"
    logger.info(f"✅ 收集代码修改请求 - ID: {request_id[:8]} | 修改人: {adjuster}")

    try:
        # 创建代码修改记录
        adjustment = CodeAdjustment(
            ai_generated_code=ai_generated_code,
            human_adjusted_code=human_adjusted_code,
            adjustment_reason=adjustment_reason,
            adjuster=adjuster,
            project_name=project_name,
            programming_language=programming_language,
            model_name=model_name,
            coding_tool=coding_tool
        )

        # 保存记录
        result = await adjustment_manager.save_adjustment(adjustment)

        logger.info(f"✅ 代码修改记录保存成功 - ID: {request_id[:8]}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ 保存代码修改记录失败 - ID: {request_id[:8]}: {str(e)}")
        error_response = create_error_response(
            "save_error",
            f"保存代码修改记录失败: {str(e)}",
            request_id=request_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "query", "data"})
async def query_adjustments(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    project_name: Optional[str] = None,
    programming_language: Optional[str] = None,
    adjuster: Optional[str] = None,
    limit: int = 10,
) -> str:
    """
    查询代码修改记录

    根据指定条件查询历史的代码修改记录。

    Args:
        start_date: 开始日期 (ISO格式，可选)
        end_date: 结束日期 (ISO格式，可选)
        project_name: 项目名称（可选）
        programming_language: 编程语言（可选）
        adjuster: 修改人（可选）
        limit: 返回记录数限制（默认10，最大100）

    Returns:
        JSON格式的字符串，包含查询结果

    Examples:
        query_adjustments(programming_language="python", limit=5)
        query_adjustments(adjuster="张三", start_date="2023-07-01T00:00:00Z")
    """
    query_id = f"query_{hash(str((start_date, end_date, project_name, adjuster)))}"
    logger.info(f"✅ 查询代码修改记录 - ID: {query_id[:8]}")

    try:
        # 创建查询条件
        query = AdjustmentQuery(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date) if end_date else None,
            project_name=project_name,
            programming_language=programming_language,
            adjuster=adjuster,
            limit=min(limit, 100)  # 限制最大返回数量
        )

        # 执行查询
        result = await adjustment_manager.query_adjustments(query)

        logger.info(f"✅ 查询完成 - ID: {query_id[:8]} | 找到 {len(result.get('adjustments', []))} 条记录")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ 查询失败 - ID: {query_id[:8]}: {str(e)}")
        error_response = create_error_response(
            "query_error",
            f"查询代码修改记录失败: {str(e)}",
            query_id=query_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "export", "data"})
async def export_adjustments(
    format: str = "json",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    project_name: Optional[str] = None,
    programming_language: Optional[str] = None,
) -> str:
    """
    导出代码修改数据

    将代码修改数据导出为指定格式的文件。

    Args:
        format: 导出格式 ("json", "csv")
        start_date: 开始日期 (ISO格式，可选)
        end_date: 结束日期 (ISO格式，可选)
        project_name: 项目名称（可选）
        programming_language: 编程语言（可选）

    Returns:
        JSON格式的字符串，包含导出结果

    Examples:
        export_adjustments(format="json")
        export_adjustments(format="csv", programming_language="python")
    """
    export_id = f"export_{hash(str((format, start_date, end_date)))}"
    logger.info(f"✅ 导出数据请求 - ID: {export_id[:8]} | 格式: {format}")

    try:
        # 创建查询条件
        query = AdjustmentQuery(
            start_date=datetime.fromisoformat(start_date) if start_date else None,
            end_date=datetime.fromisoformat(end_date) if end_date else None,
            project_name=project_name,
            programming_language=programming_language,
            limit=1000  # 导出时使用更大的限制
        )

        # 执行导出
        result = await adjustment_manager.export_adjustments(query, format)

        logger.info(f"✅ 导出完成 - ID: {export_id[:8]}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ 导出失败 - ID: {export_id[:8]}: {str(e)}")
        error_response = create_error_response(
            "export_error",
            f"导出数据失败: {str(e)}",
            export_id=export_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "stats", "analysis"})
async def get_adjustment_stats() -> str:
    """
    获取代码修改统计信息

    返回代码修改的统计分析信息，包括总数、分布、活跃用户等。

    Returns:
        JSON格式的字符串，包含统计信息

    Examples:
        get_adjustment_stats()
    """
    stats_id = f"stats_{hash(str(datetime.now()))}"
    logger.info(f"✅ 获取统计信息 - ID: {stats_id[:8]}")

    try:
        # 获取统计信息
        result = await adjustment_manager.get_stats()

        logger.info(f"✅ 统计信息获取成功 - ID: {stats_id[:8]}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ 获取统计信息失败 - ID: {stats_id[:8]}: {str(e)}")
        error_response = create_error_response(
            "stats_error",
            f"获取统计信息失败: {str(e)}",
            stats_id=stats_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


# ==================== 服务器启动 ====================

async def async_main():
    """异步主函数"""
    try:
        logger.info(f"启动 {config.name} v{config.version}")
        logger.info(f"数据目录: {config.data_dir}")

        # 运行MCP服务器
        await mcp.run_async()

    except KeyboardInterrupt:
        logger.info("服务器被用户停止")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
        sys.exit(1)


def main():
    """同步入口点函数"""
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
