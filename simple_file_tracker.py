import os
import time
from datetime import datetime

# 获取几个主要文件的元数据
files_to_check = ['main.py', 'README.md', 'pyproject.toml']

print("=== 文件修改记录分析 ===\n")

for file_path in files_to_check:
    if os.path.exists(file_path):
        stat = os.stat(file_path)
        print(f"文件: {file_path}")
        print(f"  大小: {stat.st_size} bytes")
        print(f"  修改时间: {datetime.fromtimestamp(stat.st_mtime)}")
        print(f"  访问时间: {datetime.fromtimestamp(stat.st_atime)}")
        print(f"  创建时间: {datetime.fromtimestamp(stat.st_ctime)}")
        print()

# 查找备份文件
print("=== 备份文件搜索 ===")
backup_found = False
for root, dirs, files in os.walk('.'):
    for file in files:
        if file.endswith(('.bak', '.tmp', '~', '.orig')):
            print(f"发现备份文件: {os.path.join(root, file)}")
            backup_found = True

if not backup_found:
    print("未发现备份文件")

print("\n=== IDE配置检查 ===")
ide_configs = ['.vscode', '.idea', '.vs']
for config in ide_configs:
    if os.path.exists(config):
        print(f"发现 {config} 配置目录")
    else:
        print(f"未发现 {config} 配置")
