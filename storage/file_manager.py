"""
文件管理器

提供JSON文件的读写和管理功能。
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Union

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器"""
    
    def load_json(self, file_path: Union[str, Path]) -> Union[Dict[str, Any], List[Any]]:
        """加载JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            Union[Dict[str, Any], List[Any]]: JSON数据
            
        Raises:
            FileNotFoundError: 文件不存在
            json.JSONDecodeError: JSON格式错误
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.warning(f"文件不存在: {file_path}")
            return []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.debug(f"成功加载JSON文件: {file_path}")
                return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON格式错误 {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            raise
    
    def save_json(self, file_path: Union[str, Path], data: Union[Dict[str, Any], List[Any]]) -> None:
        """保存JSON文件
        
        Args:
            file_path: 文件路径
            data: 要保存的数据
            
        Raises:
            Exception: 保存失败
        """
        file_path = Path(file_path)
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                logger.debug(f"成功保存JSON文件: {file_path}")
        except Exception as e:
            logger.error(f"保存文件失败 {file_path}: {e}")
            raise
    
    def backup_file(self, file_path: Union[str, Path], backup_suffix: str = ".backup") -> Path:
        """备份文件
        
        Args:
            file_path: 原文件路径
            backup_suffix: 备份文件后缀
            
        Returns:
            Path: 备份文件路径
        """
        file_path = Path(file_path)
        backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
        
        if file_path.exists():
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
                logger.info(f"文件备份成功: {file_path} -> {backup_path}")
                return backup_path
            except Exception as e:
                logger.error(f"文件备份失败: {e}")
                raise
        else:
            logger.warning(f"原文件不存在，无法备份: {file_path}")
            return backup_path
    
    def get_file_size(self, file_path: Union[str, Path]) -> int:
        """获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 文件大小（字节）
        """
        file_path = Path(file_path)
        if file_path.exists():
            return file_path.stat().st_size
        return 0
    
    def file_exists(self, file_path: Union[str, Path]) -> bool:
        """检查文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        return Path(file_path).exists()


# 创建全局实例
file_manager = FileManager()
