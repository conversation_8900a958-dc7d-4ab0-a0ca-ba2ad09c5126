#!/usr/bin/env python3
"""
开发辅助脚本

提供开发过程中常用的命令。
"""

import subprocess
import sys
from pathlib import Path


def run_command(cmd: str, cwd: Path = None) -> bool:
    """运行命令并返回是否成功"""
    try:
        print(f"🔧 执行: {cmd}")
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=True)
        print("✅ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python scripts/dev.py <command>")
        print("可用命令:")
        print("  setup    - 设置开发环境")
        print("  format   - 格式化代码")
        print("  lint     - 代码检查")
        print("  test     - 运行测试")
        print("  build    - 构建项目")
        print("  clean    - 清理构建文件")
        return

    command = sys.argv[1]
    project_root = Path(__file__).parent.parent

    if command == "setup":
        print("🚀 设置开发环境...")
        run_command("uv sync", project_root)
        
    elif command == "format":
        print("🎨 格式化代码...")
        run_command("uv run black .", project_root)
        run_command("uv run isort .", project_root)
        
    elif command == "lint":
        print("🔍 代码检查...")
        run_command("uv run mypy .", project_root)
        run_command("uv run black --check .", project_root)
        run_command("uv run isort --check-only .", project_root)
        
    elif command == "test":
        print("🧪 运行测试...")
        run_command("uv run pytest", project_root)
        
    elif command == "build":
        print("📦 构建项目...")
        run_command("uv build", project_root)
        
    elif command == "clean":
        print("🧹 清理构建文件...")
        import shutil
        for path in ["dist", "build", "*.egg-info"]:
            full_path = project_root / path
            if full_path.exists():
                if full_path.is_dir():
                    shutil.rmtree(full_path)
                else:
                    full_path.unlink()
        print("✅ 清理完成")
        
    else:
        print(f"❌ 未知命令: {command}")


if __name__ == "__main__":
    main()
